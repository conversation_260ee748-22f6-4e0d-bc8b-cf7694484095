import { Provider } from 'react-redux'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { store } from './store/store'
import Navigation from './components/Navigation'
import TodoList from './pages/HomePage'
import TodoDetail from './pages/TodoDetail'
import TodoAdd from './pages/TodoAdd'
import TodoUpdate from './pages/TodoUpdate'
import AboutPage from './pages/AboutPage'
import StatsPage from './pages/StatsPage'

function App() {
  return (
    <Provider store={store}>
      <Router>
        <Navigation />
        <Routes>
          <Route path='*' element={<TodoList />} />
          <Route path='/todos' element={<TodoList />} />
          <Route path='/todos/:id' element={<TodoDetail />} />
          <Route path='/todos/new' element={<TodoAdd />} />
          <Route path='/todos/:id/edit' element={<TodoUpdate />} />
          <Route path="/stats" element={<StatsPage />} />
          <Route path="/about" element={<AboutPage />} />
        </Routes>
      </Router>
    </Provider>
  )
}

export default App
