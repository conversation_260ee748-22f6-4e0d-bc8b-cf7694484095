const TodoCard = ({ todo, onDelete, onToggleComplete }) => {
  return (
    <div style={{ border: '1px solid #ccc', padding: '15px', margin: '10px 0' }}>
      <div>
        <h3 style={{ textDecoration: todo.completed ? 'line-through' : 'none' }}>
          {todo.title}
        </h3>
        <span style={{ fontSize: '12px', background: '#f0f0f0', padding: '2px 5px' }}>
          {(todo.priority || 'normal').toUpperCase()}
        </span>
      </div>

      <p style={{ textDecoration: todo.completed ? 'line-through' : 'none', margin: '10px 0' }}>
        {todo.description || 'No description provided.'}
      </p>

      <div style={{ margin: '10px 0' }}>
        <button
          onClick={() => onToggleComplete(todo)}
          style={{ marginRight: '10px', padding: '5px 10px' }}
        >
          {todo.completed ? 'Undo' : 'Complete'}
        </button>

        <button
          onClick={() => onDelete(todo.id)}
          style={{ padding: '5px 10px' }}
        >
          Delete
        </button>
      </div>
    </div>
  )
}

export default TodoCard
