import { Link, useLocation } from 'react-router-dom'

const Navigation = () => {
  const location = useLocation()

  const navStyle = {
    backgroundColor: '#f8f9fa',
    padding: '1rem',
    marginBottom: '2rem',
    borderBottom: '1px solid #dee2e6'
  }

  const navListStyle = {
    listStyle: 'none',
    display: 'flex',
    gap: '2rem',
    margin: 0,
    padding: 0
  }

  const linkStyle = {
    textDecoration: 'none',
    color: '#495057',
    fontWeight: '500',
    padding: '0.5rem 1rem',
    borderRadius: '4px',
    transition: 'background-color 0.2s'
  }

  const activeLinkStyle = {
    ...linkStyle,
    backgroundColor: '#007bff',
    color: 'white'
  }

  const isActive = (path) => {
    if (path === '/todos') {
      return location.pathname === '/todos' || location.pathname === '/'
    }
    return location.pathname.startsWith(path)
  }

  return (
    <nav style={navStyle}>
      <ul style={navListStyle}>
        <li>
          <Link
            to="/todos"
            style={isActive('/todos') ? activeLinkStyle : linkStyle}
          >
            Todos
          </Link>
        </li>
        <li>
          <Link
            to="/stats"
            style={location.pathname === '/stats' ? activeLinkStyle : linkStyle}
          >
            Stats
          </Link>
        </li>
        <li>
          <Link
            to="/about"
            style={location.pathname === '/about' ? activeLinkStyle : linkStyle}
          >
            About
          </Link>
        </li>
      </ul>
    </nav>
  )
}

export default Navigation
