import { useState } from 'react'

const TodoForm = ({ onSubmit, loading }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'medium'
  })

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!formData.title.trim()) return

    const result = await onSubmit(formData)
    if (result.success) {
      setFormData({
        title: '',
        description: '',
        priority: 'medium'
      })
    }
  }

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <form onSubmit={handleSubmit} style={{ border: '1px solid #ccc', padding: '15px', marginBottom: '20px' }}>
      <h3>Add New Todo</h3>

      <div style={{ marginBottom: '10px' }}>
        <input
          type="text"
          name="title"
          value={formData.title}
          onChange={handleChange}
          placeholder="Todo title..."
          required
          style={{ width: '100%', padding: '8px' }}
        />
      </div>

      <div style={{ marginBottom: '10px' }}>
        <textarea
          name="description"
          value={formData.description}
          onChange={handleChange}
          placeholder="Description (optional)..."
          style={{ width: '100%', padding: '8px', height: '50px' }}
        />
      </div>

      <div style={{ marginBottom: '10px' }}>
        <select
          name="priority"
          value={formData.priority}
          onChange={handleChange}
          style={{ width: '100%', padding: '8px' }}
        >
          <option value="low">Low Priority</option>
          <option value="medium">Medium Priority</option>
          <option value="high">High Priority</option>
        </select>
      </div>

      <button
        type="submit"
        disabled={loading || !formData.title.trim()}
        style={{ width: '100%', padding: '10px', backgroundColor: '#007bff', color: 'white', border: 'none' }}
      >
        {loading ? 'Adding...' : 'Add Todo'}
      </button>
    </form>
  )
}

export default TodoForm
