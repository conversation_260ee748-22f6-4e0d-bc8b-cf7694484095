const AboutPage = () => {
  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>About Todo App</h1>
      
      <div style={{ lineHeight: '1.6', fontSize: '16px' }}>
        <p>
          This is a simple Todo List application built with React and Redux Toolkit.
          It demonstrates basic CRUD operations and state management.
        </p>
        
        <h2>Features</h2>
        <ul>
          <li>Add new todos with title, description, and priority</li>
          <li>Mark todos as complete/incomplete</li>
          <li>Delete todos</li>
          <li>View statistics about your todos</li>
          <li>Data persistence using MockAPI</li>
        </ul>
        
        <h2>Technologies Used</h2>
        <ul>
          <li>React 19</li>
          <li>Redux Toolkit</li>
          <li>React Router DOM</li>
          <li>Vite</li>
          <li>MockAPI for backend</li>
        </ul>
        
        <h2>Purpose</h2>
        <p>
          This project is designed as a simple example for learning and testing purposes.
          It keeps the codebase minimal while demonstrating key React concepts.
        </p>
      </div>
    </div>
  )
}

export default AboutPage
