import { useDispatch, useSelector } from 'react-redux'
import { useEffect } from 'react'
import {
  fetchTodos,
  addTodo,
  deleteTodo,
  updateTodo,
  clearError
} from '../store/todosSlice'
import TodoCard from '../components/TodoCard'
import TodoForm from '../components/TodoForm'

const HomePage = () => {
  const dispatch = useDispatch()
  const { items: todos, loading, error } = useSelector(state => state.todos)

  // Fetch todos on mount
  useEffect(() => {
    dispatch(fetchTodos())
  }, [dispatch])

  const handleAddTodo = async (todoData) => {
    try {
      await dispatch(addTodo(todoData)).unwrap()
      return { success: true }
    } catch (error) {
      return { success: false, error }
    }
  }

  const handleDeleteTodo = async (todoId) => {
    if (window.confirm('Are you sure you want to delete this todo?')) {
      try {
        await dispatch(deleteTodo(todoId)).unwrap()
        return { success: true }
      } catch (error) {
        return { success: false, error }
      }
    }
  }

  const handleToggleComplete = async (todo) => {
    if (!todo || !todo.id) {
      return { success: false, error: 'Invalid todo object' }
    }
    try {
      await dispatch(updateTodo({
        id: todo.id,
        updates: { completed: !todo.completed },
        currentTodo: todo
      })).unwrap()
      return { success: true }
    } catch (error) {
      return { success: false, error }
    }
  }

  const handleClearError = () => {
    dispatch(clearError())
  }

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Simple Todo List</h1>

      {error && (
        <div style={{ background: '#ffebee', color: '#c62828', padding: '10px', margin: '10px 0' }}>
          Error: {error}
          <button onClick={handleClearError} style={{ float: 'right' }}>×</button>
        </div>
      )}

      <TodoForm onSubmit={handleAddTodo} loading={loading} />

      <h2>Your Todos ({todos.length})</h2>

      {loading && <p>Loading...</p>}

      {todos.length === 0 ? (
        <p>No todos yet. Add one above!</p>
      ) : (
        <div>
          {todos.map((todo) => (
            <TodoCard
              key={todo.id}
              todo={todo}
              onDelete={handleDeleteTodo}
              onToggleComplete={handleToggleComplete}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export default HomePage
