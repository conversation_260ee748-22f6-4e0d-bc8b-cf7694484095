import { useDispatch, useSelector } from 'react-redux'
import { useEffect } from 'react'
import { Link } from 'react-router-dom'
import {
  fetchTodos,
  deleteTodo,
  updateTodo,
  clearError
} from '../store/todosSlice'

const TodoList = () => {
  const dispatch = useDispatch()
  const { items: todos, loading, error } = useSelector(state => state.todos)

  // Fetch todos on mount
  useEffect(() => {
    dispatch(fetchTodos())
  }, [dispatch])

  const handleDeleteTodo = async (todoId) => {
    if (window.confirm('Are you sure you want to delete this todo?')) {
      try {
        await dispatch(deleteTodo(todoId)).unwrap()
      } catch (error) {
        console.error('Delete failed:', error)
      }
    }
  }

  const handleToggleComplete = async (todo) => {
    if (!todo || !todo.id) return
    try {
      await dispatch(updateTodo({
        id: todo.id,
        updates: { completed: !todo.completed },
        currentTodo: todo
      })).unwrap()
    } catch (error) {
      console.error('Toggle failed:', error)
    }
  }

  const handleClearError = () => {
    dispatch(clearError())
  }

  const todoCardStyle = {
    border: '1px solid #ddd',
    borderRadius: '8px',
    padding: '15px',
    margin: '10px 0',
    backgroundColor: '#f9f9f9'
  }

  const buttonStyle = {
    padding: '5px 10px',
    margin: '0 5px',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer'
  }

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
        <h1>Todo List</h1>
        <Link
          to="/add"
          style={{
            ...buttonStyle,
            backgroundColor: '#007bff',
            color: 'white',
            textDecoration: 'none'
          }}
        >
          Add New Todo
        </Link>
      </div>

      {error && (
        <div style={{ background: '#ffebee', color: '#c62828', padding: '10px', margin: '10px 0' }}>
          Error: {error}
          <button onClick={handleClearError} style={{ float: 'right' }}>×</button>
        </div>
      )}

      <h2>Your Todos ({todos.length})</h2>

      {loading && <p>Loading...</p>}

      {todos.length === 0 ? (
        <p>No todos yet. <Link to="/add">Add one now!</Link></p>
      ) : (
        <div>
          {todos.map((todo) => (
            <div key={todo.id} style={todoCardStyle}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div style={{ flex: 1 }}>
                  <h3 style={{
                    margin: '0 0 5px 0',
                    textDecoration: todo.completed ? 'line-through' : 'none',
                    color: todo.completed ? '#666' : '#000'
                  }}>
                    {todo.title}
                  </h3>
                  {todo.description && (
                    <p style={{ margin: '5px 0', color: '#666' }}>{todo.description}</p>
                  )}
                  <span style={{
                    fontSize: '12px',
                    padding: '2px 6px',
                    borderRadius: '3px',
                    backgroundColor: todo.priority === 'high' ? '#ffebee' :
                                   todo.priority === 'medium' ? '#fff3e0' : '#e8f5e8',
                    color: todo.priority === 'high' ? '#c62828' :
                           todo.priority === 'medium' ? '#ef6c00' : '#2e7d32'
                  }}>
                    {todo.priority || 'medium'} priority
                  </span>
                </div>
                <div style={{ display: 'flex', gap: '5px' }}>
                  <button
                    onClick={() => handleToggleComplete(todo)}
                    style={{
                      ...buttonStyle,
                      backgroundColor: todo.completed ? '#6c757d' : '#28a745',
                      color: 'white'
                    }}
                  >
                    {todo.completed ? 'Undo' : 'Complete'}
                  </button>
                  <button
                    onClick={() => handleDeleteTodo(todo.id)}
                    style={{
                      ...buttonStyle,
                      backgroundColor: '#dc3545',
                      color: 'white'
                    }}
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default TodoList
