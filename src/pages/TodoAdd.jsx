import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { addTodo } from '../store/todosSlice'

const TodoAdd = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { loading } = useSelector(state => state.todos)

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'medium'
  })

  const [errors, setErrors] = useState({})

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!validateForm()) return

    try {
      const todoData = {
        ...formData,
        title: formData.title.trim(),
        description: formData.description.trim(),
        completed: false,
        createdAt: new Date().toISOString()
      }

      await dispatch(addTodo(todoData)).unwrap()
      navigate('/')
    } catch (error) {
      console.error('Add todo failed:', error)
      setErrors({ submit: 'Failed to add todo. Please try again.' })
    }
  }

  const inputStyle = {
    width: '100%',
    padding: '10px',
    fontSize: '16px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    marginBottom: '5px'
  }

  const errorStyle = {
    color: '#dc3545',
    fontSize: '14px',
    marginBottom: '10px'
  }

  const buttonStyle = {
    padding: '10px 20px',
    fontSize: '16px',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    marginRight: '10px'
  }

  return (
    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <div style={{ marginBottom: '20px' }}>
        <Link to="/" style={{ color: '#007bff', textDecoration: 'none' }}>
          ← Back to Todo List
        </Link>
      </div>

      <h1>Add New Todo</h1>

      <form onSubmit={handleSubmit} style={{
        border: '1px solid #ddd',
        borderRadius: '8px',
        padding: '30px',
        backgroundColor: '#f9f9f9'
      }}>
        {errors.submit && (
          <div style={{ ...errorStyle, backgroundColor: '#ffebee', padding: '10px', borderRadius: '4px', marginBottom: '20px' }}>
            {errors.submit}
          </div>
        )}

        <div style={{ marginBottom: '20px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            Title *
          </label>
          <input
            type="text"
            name="title"
            value={formData.title}
            onChange={handleChange}
            placeholder="Enter todo title..."
            style={{
              ...inputStyle,
              borderColor: errors.title ? '#dc3545' : '#ddd'
            }}
          />
          {errors.title && <div style={errorStyle}>{errors.title}</div>}
        </div>

        <div style={{ marginBottom: '20px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="Enter description (optional)..."
            rows="4"
            style={{
              ...inputStyle,
              resize: 'vertical',
              minHeight: '80px'
            }}
          />
        </div>

        <div style={{ marginBottom: '30px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            Priority
          </label>
          <select
            name="priority"
            value={formData.priority}
            onChange={handleChange}
            style={inputStyle}
          >
            <option value="low">Low Priority</option>
            <option value="medium">Medium Priority</option>
            <option value="high">High Priority</option>
          </select>
        </div>

        <div style={{ borderTop: '1px solid #ddd', paddingTop: '20px' }}>
          <button
            type="submit"
            disabled={loading}
            style={{
              ...buttonStyle,
              backgroundColor: loading ? '#6c757d' : '#28a745',
              color: 'white',
              opacity: loading ? 0.6 : 1
            }}
          >
            {loading ? 'Adding...' : 'Add Todo'}
          </button>

          <Link
            to="/"
            style={{
              ...buttonStyle,
              backgroundColor: '#6c757d',
              color: 'white',
              textDecoration: 'none',
              display: 'inline-block'
            }}
          >
            Cancel
          </Link>
        </div>
      </form>
    </div>
  )
}

export default TodoAdd
