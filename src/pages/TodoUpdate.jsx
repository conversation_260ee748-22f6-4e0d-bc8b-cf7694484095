import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom'
import { useDispatch, useSelector } from 'react-redux'
import { fetchTodos, updateTodo } from '../store/todosSlice'

const TodoUpdate = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { items: todos, loading } = useSelector(state => state.todos)
  
  const todo = todos.find(t => t.id === id)
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'medium',
    completed: false
  })
  
  const [errors, setErrors] = useState({})

  useEffect(() => {
    if (todos.length === 0) {
      dispatch(fetchTodos())
    }
  }, [dispatch, todos.length])

  useEffect(() => {
    if (todo) {
      setFormData({
        title: todo.title || '',
        description: todo.description || '',
        priority: todo.priority || 'medium',
        completed: todo.completed || false
      })
    }
  }, [todo])

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}
    
    if (!formData.title.trim()) {
      newErrors.title = 'Title is required'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) return
    
    try {
      const updates = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        priority: formData.priority,
        completed: formData.completed
      }
      
      await dispatch(updateTodo({
        id: id,
        updates: updates,
        currentTodo: todo
      })).unwrap()
      
      navigate(`/todos/${id}`)
    } catch (error) {
      console.error('Update todo failed:', error)
      setErrors({ submit: 'Failed to update todo. Please try again.' })
    }
  }

  const inputStyle = {
    width: '100%',
    padding: '10px',
    fontSize: '16px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    marginBottom: '5px'
  }

  const errorStyle = {
    color: '#dc3545',
    fontSize: '14px',
    marginBottom: '10px'
  }

  const buttonStyle = {
    padding: '10px 20px',
    fontSize: '16px',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    marginRight: '10px'
  }

  if (loading && !todo) {
    return (
      <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
        <p>Loading...</p>
      </div>
    )
  }

  if (!todo) {
    return (
      <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
        <h1>Todo Not Found</h1>
        <p>The todo you're trying to edit doesn't exist.</p>
        <Link to="/todos" style={{ color: '#007bff', textDecoration: 'none' }}>
          ← Back to Todo List
        </Link>
      </div>
    )
  }

  return (
    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <div style={{ marginBottom: '20px' }}>
        <Link to={`/todos/${id}`} style={{ color: '#007bff', textDecoration: 'none' }}>
          ← Back to Todo Detail
        </Link>
      </div>

      <h1>Edit Todo</h1>

      <form onSubmit={handleSubmit} style={{
        border: '1px solid #ddd',
        borderRadius: '8px',
        padding: '30px',
        backgroundColor: '#f9f9f9'
      }}>
        {errors.submit && (
          <div style={{ ...errorStyle, backgroundColor: '#ffebee', padding: '10px', borderRadius: '4px', marginBottom: '20px' }}>
            {errors.submit}
          </div>
        )}

        <div style={{ marginBottom: '20px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            Title *
          </label>
          <input
            type="text"
            name="title"
            value={formData.title}
            onChange={handleChange}
            placeholder="Enter todo title..."
            style={{
              ...inputStyle,
              borderColor: errors.title ? '#dc3545' : '#ddd'
            }}
          />
          {errors.title && <div style={errorStyle}>{errors.title}</div>}
        </div>

        <div style={{ marginBottom: '20px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="Enter description (optional)..."
            rows="4"
            style={{
              ...inputStyle,
              resize: 'vertical',
              minHeight: '80px'
            }}
          />
        </div>

        <div style={{ marginBottom: '20px' }}>
          <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
            Priority
          </label>
          <select
            name="priority"
            value={formData.priority}
            onChange={handleChange}
            style={inputStyle}
          >
            <option value="low">Low Priority</option>
            <option value="medium">Medium Priority</option>
            <option value="high">High Priority</option>
          </select>
        </div>

        <div style={{ marginBottom: '30px' }}>
          <label style={{ display: 'flex', alignItems: 'center', fontWeight: 'bold' }}>
            <input
              type="checkbox"
              name="completed"
              checked={formData.completed}
              onChange={handleChange}
              style={{ marginRight: '8px' }}
            />
            Mark as completed
          </label>
        </div>

        <div style={{ borderTop: '1px solid #ddd', paddingTop: '20px' }}>
          <button
            type="submit"
            disabled={loading}
            style={{
              ...buttonStyle,
              backgroundColor: loading ? '#6c757d' : '#007bff',
              color: 'white',
              opacity: loading ? 0.6 : 1
            }}
          >
            {loading ? 'Updating...' : 'Update Todo'}
          </button>
          
          <Link
            to={`/todos/${id}`}
            style={{
              ...buttonStyle,
              backgroundColor: '#6c757d',
              color: 'white',
              textDecoration: 'none',
              display: 'inline-block'
            }}
          >
            Cancel
          </Link>
        </div>
      </form>
    </div>
  )
}

export default TodoUpdate
