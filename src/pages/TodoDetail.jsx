import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom'
import { useSelector, useDispatch } from 'react-redux'
import { useEffect } from 'react'
import { fetchTodos, deleteTodo, updateTodo } from '../store/todosSlice'

const TodoDetail = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { items: todos, loading } = useSelector(state => state.todos)
  
  const todo = todos.find(t => t.id === id)

  useEffect(() => {
    if (todos.length === 0) {
      dispatch(fetchTodos())
    }
  }, [dispatch, todos.length])

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this todo?')) {
      try {
        await dispatch(deleteTodo(id)).unwrap()
        navigate('/todos')
      } catch (error) {
        console.error('Delete failed:', error)
      }
    }
  }

  const handleToggleComplete = async () => {
    if (!todo) return
    try {
      await dispatch(updateTodo({
        id: todo.id,
        updates: { completed: !todo.completed },
        currentTodo: todo
      })).unwrap()
    } catch (error) {
      console.error('Toggle failed:', error)
    }
  }

  const buttonStyle = {
    padding: '10px 15px',
    margin: '0 5px',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    textDecoration: 'none',
    display: 'inline-block'
  }

  if (loading) {
    return (
      <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
        <p>Loading...</p>
      </div>
    )
  }

  if (!todo) {
    return (
      <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
        <h1>Todo Not Found</h1>
        <p>The todo you're looking for doesn't exist.</p>
        <Link to="/todos" style={{ ...buttonStyle, backgroundColor: '#007bff', color: 'white' }}>
          Back to Todo List
        </Link>
      </div>
    )
  }

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <div style={{ marginBottom: '20px' }}>
        <Link to="/todos" style={{ color: '#007bff', textDecoration: 'none' }}>
          ← Back to Todo List
        </Link>
      </div>

      <div style={{
        border: '1px solid #ddd',
        borderRadius: '8px',
        padding: '30px',
        backgroundColor: '#f9f9f9'
      }}>
        <div style={{ marginBottom: '20px' }}>
          <h1 style={{
            margin: '0 0 10px 0',
            textDecoration: todo.completed ? 'line-through' : 'none',
            color: todo.completed ? '#666' : '#000'
          }}>
            {todo.title}
          </h1>
          
          <div style={{ marginBottom: '15px' }}>
            <span style={{
              fontSize: '14px',
              padding: '4px 8px',
              borderRadius: '4px',
              backgroundColor: todo.priority === 'high' ? '#ffebee' : 
                             todo.priority === 'medium' ? '#fff3e0' : '#e8f5e8',
              color: todo.priority === 'high' ? '#c62828' : 
                     todo.priority === 'medium' ? '#ef6c00' : '#2e7d32',
              marginRight: '10px'
            }}>
              {todo.priority || 'medium'} priority
            </span>
            
            <span style={{
              fontSize: '14px',
              padding: '4px 8px',
              borderRadius: '4px',
              backgroundColor: todo.completed ? '#e8f5e8' : '#fff3e0',
              color: todo.completed ? '#2e7d32' : '#ef6c00'
            }}>
              {todo.completed ? 'Completed' : 'Pending'}
            </span>
          </div>
        </div>

        {todo.description && (
          <div style={{ marginBottom: '20px' }}>
            <h3>Description</h3>
            <p style={{ lineHeight: '1.6', color: '#555' }}>{todo.description}</p>
          </div>
        )}

        {todo.createdAt && (
          <div style={{ marginBottom: '20px' }}>
            <h3>Created</h3>
            <p style={{ color: '#666' }}>
              {new Date(todo.createdAt).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </p>
          </div>
        )}

        <div style={{ borderTop: '1px solid #ddd', paddingTop: '20px', marginTop: '30px' }}>
          <h3>Actions</h3>
          <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
            <Link
              to={`/todos/${todo.id}/edit`}
              style={{
                ...buttonStyle,
                backgroundColor: '#ffc107',
                color: 'black'
              }}
            >
              Edit Todo
            </Link>
            
            <button
              onClick={handleToggleComplete}
              style={{
                ...buttonStyle,
                backgroundColor: todo.completed ? '#6c757d' : '#28a745',
                color: 'white'
              }}
            >
              {todo.completed ? 'Mark as Pending' : 'Mark as Complete'}
            </button>
            
            <button
              onClick={handleDelete}
              style={{
                ...buttonStyle,
                backgroundColor: '#dc3545',
                color: 'white'
              }}
            >
              Delete Todo
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TodoDetail
