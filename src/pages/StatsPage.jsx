import { useSelector } from 'react-redux'

const StatsPage = () => {
  const { items: todos } = useSelector(state => state.todos)

  const totalTodos = todos.length
  const completedTodos = todos.filter(todo => todo.completed).length
  const pendingTodos = totalTodos - completedTodos
  const completionRate = totalTodos > 0 ? Math.round((completedTodos / totalTodos) * 100) : 0

  // Priority stats
  const priorityStats = todos.reduce((acc, todo) => {
    const priority = todo.priority || 'medium'
    acc[priority] = (acc[priority] || 0) + 1
    return acc
  }, {})

  const statCardStyle = {
    border: '1px solid #ddd',
    borderRadius: '8px',
    padding: '20px',
    margin: '10px',
    textAlign: 'center',
    backgroundColor: '#f8f9fa'
  }

  const statNumberStyle = {
    fontSize: '2rem',
    fontWeight: 'bold',
    color: '#007bff',
    margin: '10px 0'
  }

  const gridStyle = {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
    gap: '20px',
    marginBottom: '30px'
  }

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Todo Statistics</h1>
      
      <div style={gridStyle}>
        <div style={statCardStyle}>
          <h3>Total Todos</h3>
          <div style={statNumberStyle}>{totalTodos}</div>
        </div>
        
        <div style={statCardStyle}>
          <h3>Completed</h3>
          <div style={{ ...statNumberStyle, color: '#28a745' }}>{completedTodos}</div>
        </div>
        
        <div style={statCardStyle}>
          <h3>Pending</h3>
          <div style={{ ...statNumberStyle, color: '#dc3545' }}>{pendingTodos}</div>
        </div>
        
        <div style={statCardStyle}>
          <h3>Completion Rate</h3>
          <div style={statNumberStyle}>{completionRate}%</div>
        </div>
      </div>

      <div style={{ marginTop: '30px' }}>
        <h2>Priority Breakdown</h2>
        <div style={gridStyle}>
          <div style={statCardStyle}>
            <h4>High Priority</h4>
            <div style={{ ...statNumberStyle, color: '#dc3545' }}>
              {priorityStats.high || 0}
            </div>
          </div>
          
          <div style={statCardStyle}>
            <h4>Medium Priority</h4>
            <div style={{ ...statNumberStyle, color: '#ffc107' }}>
              {priorityStats.medium || 0}
            </div>
          </div>
          
          <div style={statCardStyle}>
            <h4>Low Priority</h4>
            <div style={{ ...statNumberStyle, color: '#28a745' }}>
              {priorityStats.low || 0}
            </div>
          </div>
        </div>
      </div>

      {totalTodos === 0 && (
        <div style={{ textAlign: 'center', marginTop: '40px', color: '#6c757d' }}>
          <p>No todos yet! Add some todos to see statistics.</p>
        </div>
      )}
    </div>
  )
}

export default StatsPage
