# 📝 Hướng dẫn nhanh cho bài thi

## 🚀 Khởi chạy nhanh

```bash
npm install
npm run dev
```

Mở: http://localhost:5173

## 📋 Chức năng chính

1. **Thêm todo**: Điền form và click "Add Todo"
2. **Complete/Undo**: Click nút "Complete" hoặc "Undo"
3. **Xóa**: Click nút "Delete"

## 🏗️ Cấu trúc code đơn giản

### Components chính:
- `src/App.jsx` - Component gốc
- `src/pages/HomePage.jsx` - Trang chính duy nhất (chứa tất cả logic)
- `src/components/TodoForm.jsx` - Form thêm todo
- `src/components/TodoCard.jsx` - Hiển thị từng todo

### Redux:
- `src/store/store.js` - Redux store
- `src/store/todosSlice.js` - Todos slice với API calls

### API:
- MockAPI endpoint: `https://67cd350bdd7651e464eda2a9.mockapi.io/todos`
- Tự động lưu trữ dữ liệu trên cloud

## 🔧 Các thao tác API

- **GET** `/todos` - Lấy danh sách
- **POST** `/todos` - Thêm mới
- **PUT** `/todos/:id` - Cập nhật
- **DELETE** `/todos/:id` - Xóa

## 💡 Tips cho bài thi

1. **Đơn giản**: Không có routing phức tạp, chỉ 1 trang
2. **Tự động**: API tự động lưu, không cần setup server
3. **Hoàn chỉnh**: Có đủ CRUD operations
4. **Redux**: Sử dụng Redux Toolkit hiện đại
5. **Responsive**: Layout đơn giản, dễ demo

## 🎯 Điểm mạnh để trình bày

- ✅ React functional components với hooks
- ✅ Redux Toolkit cho state management
- ✅ Async operations với createAsyncThunk
- ✅ API integration với axios
- ✅ Error handling cơ bản
- ✅ Loading states
- ✅ Form validation
- ✅ Responsive design đơn giản

## 🚨 Lưu ý

- Không cần setup database
- Không cần authentication
- UI đơn giản, tập trung vào functionality
- Code clean và dễ hiểu
- Phù hợp cho demo trong thời gian ngắn
