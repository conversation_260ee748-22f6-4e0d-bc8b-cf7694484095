# 📝 Hướng dẫn nhanh cho bài thi

## 🚀 Khởi chạy nhanh

```bash
npm install
npm run dev
```

Mở: http://localhost:5173

## 📋 Chức năng chính

1. **Điều hướng**: Sử dụng menu để chuyển trang (Todos, Add Todo, Stats, About)
2. **Thêm todo**: Click "Add Todo" trong menu hoặc button, điền form
3. **Complete/Undo**: Click nút "Complete" hoặc "Undo" ở danh sách
4. **Xóa**: Click nút "Delete" ở danh sách
5. **Xem thống kê**: <PERSON><PERSON><PERSON><PERSON> sang trang Stats

## 🏗️ Cấu trúc code đơn giản

### Components chính:
- `src/App.jsx` - Component gốc với Router (4 routes đơn giản)
- `src/components/Navigation.jsx` - <PERSON>h điều hướng
- `src/pages/HomePage.jsx` - <PERSON>rang chính (danh sách todos)
- `src/pages/TodoAdd.jsx` - Trang thêm todo
- `src/pages/StatsPage.jsx` - Trang thống kê
- `src/pages/AboutPage.jsx` - Trang giới thiệu

### Redux:
- `src/store/store.js` - Redux store
- `src/store/todosSlice.js` - Todos slice với API calls

### API:
- MockAPI endpoint: `https://67cd350bdd7651e464eda2a9.mockapi.io/todos`
- Tự động lưu trữ dữ liệu trên cloud

## 🔧 Các thao tác API

- **GET** `/todos` - Lấy danh sách
- **POST** `/todos` - Thêm mới
- **PUT** `/todos/:id` - Cập nhật
- **DELETE** `/todos/:id` - Xóa

## 💡 Tips cho bài thi

1. **Cực đơn giản**: Chỉ 4 routes cơ bản (/, /add, /stats, /about)
2. **Tự động**: API tự động lưu, không cần setup server
3. **Hoàn chỉnh**: Có đủ CRUD operations + navigation
4. **Redux**: Sử dụng Redux Toolkit hiện đại
5. **Router**: React Router DOM đơn giản nhất
6. **Nhanh**: Phù hợp cho thi cử, tiết kiệm thời gian

## 🎯 Điểm mạnh để trình bày

- ✅ React functional components với hooks
- ✅ Redux Toolkit cho state management
- ✅ React Router DOM cho navigation
- ✅ Async operations với createAsyncThunk
- ✅ API integration với axios
- ✅ Error handling cơ bản
- ✅ Loading states
- ✅ Form validation
- ✅ Statistics page với data visualization
- ✅ Responsive design đơn giản

## 🚨 Lưu ý

- Không cần setup database
- Không cần authentication
- UI đơn giản, tập trung vào functionality
- Code clean và dễ hiểu
- Phù hợp cho demo trong thời gian ngắn
