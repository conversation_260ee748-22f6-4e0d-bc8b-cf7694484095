# 📝 Hướng dẫn nhanh cho bài thi

## 🚀 Khởi chạy nhanh

```bash
npm install
npm run dev
```

Mở: http://localhost:5173

## 📋 Chức năng chính

1. **Điều hướng**: Sử dụng menu để chuyển trang (Home, Stats, About)
2. **Thêm todo**: Điền form và click "Add Todo"
3. **Complete/Undo**: Click nút "Complete" hoặc "Undo"
4. **Xóa**: Click nút "Delete"
5. **Xem thống kê**: <PERSON><PERSON><PERSON><PERSON> sang trang Stats

## 🏗️ Cấu trúc code đơn giản

### Components chính:
- `src/App.jsx` - Component gốc với Router
- `src/components/Navigation.jsx` - Thanh điều hướng
- `src/pages/HomePage.jsx` - Trang chín<PERSON> (CRUD todos)
- `src/pages/StatsPage.jsx` - Trang thống kê
- `src/pages/AboutPage.jsx` - Trang giới thiệu
- `src/components/TodoForm.jsx` - Form thêm todo
- `src/components/TodoCard.jsx` - Hiển thị từng todo

### Redux:
- `src/store/store.js` - Redux store
- `src/store/todosSlice.js` - Todos slice với API calls

### API:
- MockAPI endpoint: `https://67cd350bdd7651e464eda2a9.mockapi.io/todos`
- Tự động lưu trữ dữ liệu trên cloud

## 🔧 Các thao tác API

- **GET** `/todos` - Lấy danh sách
- **POST** `/todos` - Thêm mới
- **PUT** `/todos/:id` - Cập nhật
- **DELETE** `/todos/:id` - Xóa

## 💡 Tips cho bài thi

1. **Đơn giản**: Routing cơ bản với 3 trang
2. **Tự động**: API tự động lưu, không cần setup server
3. **Hoàn chỉnh**: Có đủ CRUD operations + navigation
4. **Redux**: Sử dụng Redux Toolkit hiện đại
5. **Router**: React Router DOM cho SPA
6. **Responsive**: Layout đơn giản, dễ demo

## 🎯 Điểm mạnh để trình bày

- ✅ React functional components với hooks
- ✅ Redux Toolkit cho state management
- ✅ React Router DOM cho navigation
- ✅ Async operations với createAsyncThunk
- ✅ API integration với axios
- ✅ Error handling cơ bản
- ✅ Loading states
- ✅ Form validation
- ✅ Statistics page với data visualization
- ✅ Responsive design đơn giản

## 🚨 Lưu ý

- Không cần setup database
- Không cần authentication
- UI đơn giản, tập trung vào functionality
- Code clean và dễ hiểu
- Phù hợp cho demo trong thời gian ngắn
