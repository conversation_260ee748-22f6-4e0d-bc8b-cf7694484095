# RESTful Routes - Todo App

## 🛣️ Route Structure

Ứng dụng sử dụng RESTful routing pattern theo mẫu:

```javascript
<Routes>
  <Route path='*' element={<TodoList />} />
  <Route path='/todos' element={<TodoList />} />
  <Route path='/todos/:id' element={<TodoDetail />} />
  <Route path='/todos/new' element={<TodoAdd />} />
  <Route path='/todos/:id/edit' element={<TodoUpdate />} />
  <Route path="/stats" element={<StatsPage />} />
  <Route path="/about" element={<AboutPage />} />
</Routes>
```

## 📋 Chi tiết Routes

### 1. `*` - Default Route
- **Component**: `TodoList`
- **Mục đích**: Fallback cho tất cả routes không match
- **Redirect**: Hiển thị danh sách todos

### 2. `/todos` - Index Route
- **Component**: `TodoList`
- **Mục đích**: Hi<PERSON>n thị danh sách tất cả todos
- **Features**:
  - <PERSON>h sách todos với pagination
  - Buttons: View, Edit, Complete/Undo, Delete
  - Link "Add New Todo"
  - Search và filter (có thể mở rộng)

### 3. `/todos/:id` - Show Route
- **Component**: `TodoDetail`
- **Mục đích**: Hiển thị chi tiết một todo
- **Features**:
  - Thông tin đầy đủ của todo
  - Actions: Edit, Toggle Complete, Delete
  - Breadcrumb navigation
  - Created date/time

### 4. `/todos/new` - New Route
- **Component**: `TodoAdd`
- **Mục đích**: Form thêm todo mới
- **Features**:
  - Form validation
  - Error handling
  - Cancel button
  - Redirect sau khi thêm thành công

### 5. `/todos/:id/edit` - Edit Route
- **Component**: `TodoUpdate`
- **Mục đích**: Form sửa todo existing
- **Features**:
  - Pre-populate data
  - Form validation
  - Completed checkbox
  - Cancel button quay về detail

### 6. `/stats` - Stats Route
- **Component**: `StatsPage`
- **Mục đích**: Thống kê và analytics
- **Features**:
  - Total, completed, pending counts
  - Completion rate
  - Priority breakdown

### 7. `/about` - About Route
- **Component**: `AboutPage`
- **Mục đích**: Thông tin về ứng dụng
- **Features**:
  - Project description
  - Technologies used
  - Features list

## 🔄 Navigation Flow

```
TodoList (/todos)
├── View → TodoDetail (/todos/:id)
│   ├── Edit → TodoUpdate (/todos/:id/edit)
│   │   ├── Save → TodoDetail (/todos/:id)
│   │   └── Cancel → TodoDetail (/todos/:id)
│   ├── Delete → TodoList (/todos)
│   └── Back → TodoList (/todos)
├── Edit → TodoUpdate (/todos/:id/edit)
├── Add New → TodoAdd (/todos/new)
│   ├── Save → TodoList (/todos)
│   └── Cancel → TodoList (/todos)
└── Stats → StatsPage (/stats)
```

## 🎯 RESTful Principles

### 1. **Resource-based URLs**
- `/todos` - Collection of todos
- `/todos/:id` - Specific todo resource
- `/todos/new` - Form to create new todo
- `/todos/:id/edit` - Form to edit existing todo

### 2. **Predictable Patterns**
- List: `/todos`
- Show: `/todos/:id`
- New: `/todos/new`
- Edit: `/todos/:id/edit`

### 3. **Nested Resources**
- Edit form is nested under specific todo
- Clear parent-child relationship

### 4. **Semantic Actions**
- URLs describe what you're doing
- Easy to understand and remember

## 🚀 Lợi ích cho bài thi

1. **Professional Structure**: Theo chuẩn RESTful
2. **Scalable**: Dễ thêm features mới
3. **User-friendly**: URLs có ý nghĩa
4. **SEO-friendly**: URLs descriptive
5. **Maintainable**: Cấu trúc rõ ràng
6. **Industry Standard**: Theo best practices

## 💡 Tips Implementation

### Route Order quan trọng:
```javascript
// ✅ Đúng - specific routes trước
<Route path='/todos/new' element={<TodoAdd />} />
<Route path='/todos/:id/edit' element={<TodoUpdate />} />
<Route path='/todos/:id' element={<TodoDetail />} />

// ❌ Sai - dynamic route sẽ catch 'new'
<Route path='/todos/:id' element={<TodoDetail />} />
<Route path='/todos/new' element={<TodoAdd />} />
```

### Navigation Logic:
```javascript
// Active state cho nested routes
const isActive = (path) => {
  if (path === '/todos') {
    return location.pathname === '/todos' || location.pathname === '/'
  }
  return location.pathname.startsWith(path)
}
```

## ⏰ Thời gian implement

- TodoList (refactor): 10 phút
- TodoDetail: 15 phút
- TodoAdd: 10 phút
- TodoUpdate: 15 phút
- Navigation update: 5 phút
- App.jsx routes: 5 phút

**Total: ~60 phút**

Hoàn hảo cho bài thi vì thể hiện được kiến thức sâu về React Router và RESTful design!
